package com.xiang.proxy.server.diagnostic;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据丢失诊断工具测试
 */
public class DataLossDiagnosticTest {
    
    private DataLossDiagnostic diagnostic;
    
    @BeforeEach
    void setUp() {
        diagnostic = DataLossDiagnostic.getInstance();
        diagnostic.startDiagnostic();
    }
    
    @AfterEach
    void tearDown() {
        diagnostic.stopDiagnostic();
    }
    
    @Test
    void testBasicStatistics() {
        // 模拟连接创建
        diagnostic.recordConnectionCreated("conn1", "example.com:80");
        diagnostic.recordConnectionCreated("conn2", "example.com:443");
        
        // 模拟数据发送
        diagnostic.recordDataSent("conn1", 1024);
        diagnostic.recordDataSent("conn2", 2048);
        
        // 模拟数据接收
        diagnostic.recordDataReceived("conn1", 512);
        diagnostic.recordDataReceived("conn2", 1024);
        
        // 模拟路由操作
        diagnostic.recordRouteRegistration("conn1", 1001);
        diagnostic.recordRouteRegistration("conn2", 1002);
        diagnostic.recordRouteMiss("conn3", 256);
        
        // 验证统计数据
        DataLossDiagnostic.DiagnosticStats stats = diagnostic.getStats();
        assertEquals(2, stats.totalConnectionsCreated);
        assertEquals(3072, stats.totalDataSent);
        assertEquals(1536, stats.totalDataReceived);
        assertEquals(2, stats.routeRegistrations);
        assertEquals(1, stats.routeMisses);
        
        // 验证计算结果
        assertEquals(50.0, stats.getDataReceiveRate(), 0.01);
        assertEquals(50.0, stats.getRouteMissRate(), 0.01);
        assertEquals(2, stats.getActiveRoutes());
    }
    
    @Test
    void testDataReceiveRateCalculation() {
        // 测试数据接收率计算
        diagnostic.recordDataSent("conn1", 1000);
        diagnostic.recordDataReceived("conn1", 800);
        
        DataLossDiagnostic.DiagnosticStats stats = diagnostic.getStats();
        assertEquals(80.0, stats.getDataReceiveRate(), 0.01);
    }
    
    @Test
    void testRouteMissRateCalculation() {
        // 测试路由未命中率计算
        diagnostic.recordRouteRegistration("conn1", 1001);
        diagnostic.recordRouteRegistration("conn2", 1002);
        diagnostic.recordRouteRegistration("conn3", 1003);
        diagnostic.recordRouteMiss("conn4", 100);
        
        DataLossDiagnostic.DiagnosticStats stats = diagnostic.getStats();
        assertEquals(25.0, stats.getRouteMissRate(), 0.01);
    }
    
    @Test
    void testActiveRoutesCalculation() {
        // 测试活跃路由数量计算
        diagnostic.recordRouteRegistration("conn1", 1001);
        diagnostic.recordRouteRegistration("conn2", 1002);
        diagnostic.recordRouteRegistration("conn3", 1003);
        diagnostic.recordRouteUnregistration("conn1");
        
        DataLossDiagnostic.DiagnosticStats stats = diagnostic.getStats();
        assertEquals(2, stats.getActiveRoutes());
    }
    
    @Test
    void testZeroDivisionHandling() {
        // 测试零除法处理
        DataLossDiagnostic.DiagnosticStats stats = diagnostic.getStats();
        assertEquals(0.0, stats.getDataReceiveRate(), 0.01);
        assertEquals(0.0, stats.getRouteMissRate(), 0.01);
        assertEquals(0, stats.getActiveRoutes());
    }
}
